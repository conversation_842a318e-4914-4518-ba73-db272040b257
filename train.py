import warnings
import os
import cv2
from pathlib import Path
import torch
import torch.nn as nn
import json
import time
from tqdm import tqdm

warnings.filterwarnings('ignore')
from ultralytics import YOLO

# 导入OCR相关库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("EasyOCR not available. Installing...")
    os.system("pip install easyocr")
    import easyocr
    EASYOCR_AVAILABLE = True

try:
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True
except ImportError:
    CNOCR_AVAILABLE = False
    print("CnOCR not available. Installing...")
    os.system("pip install cnocr")
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True


class MultiTaskModel(nn.Module):
    """
    多任务模型：结合YOLO目标检测和OCR文字识别
    """
    def __init__(self, yolo_model_path, num_classes=47):
        super(MultiTaskModel, self).__init__()

        # 加载YOLO模型
        self.yolo_model = YOLO(yolo_model_path)
        self.num_classes = num_classes

        # 初始化OCR引擎
        self.init_ocr_engines()

        # 文字识别置信度阈值
        self.ocr_confidence_threshold = 0.5

    def init_ocr_engines(self):
        """初始化多个OCR引擎以提高识别精度"""
        self.ocr_engines = {}

        # EasyOCR引擎 - 支持中英文
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                print("✓ EasyOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ EasyOCR引擎初始化失败: {e}")

        # CnOCR引擎 - 高精度中文识别
        if CNOCR_AVAILABLE:
            try:
                self.ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',  # 轻量级高精度模型
                    det_model_name='db_resnet18',  # 文字检测模型
                    use_gpu=torch.cuda.is_available()
                )
                print("✓ CnOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ CnOCR引擎初始化失败: {e}")

    def detect_objects(self, image_path, conf_threshold=0.25):
        """
        使用YOLO进行目标检测
        """
        results = self.yolo_model.predict(
            image_path,
            conf=conf_threshold,
            device='0' if torch.cuda.is_available() else 'cpu',
            verbose=False
        )
        return results

    def extract_text_regions(self, image, detection_results):
        """
        从检测结果中提取可能包含文字的区域
        """
        text_regions = []

        if len(detection_results) > 0 and len(detection_results[0].boxes) > 0:
            boxes = detection_results[0].boxes

            for box in boxes:
                # 获取边界框坐标
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = float(box.conf[0])
                class_id = int(box.cls[0])

                # 扩展边界框以包含可能的文字区域
                h, w = image.shape[:2]
                margin = 10
                x1 = max(0, int(x1) - margin)
                y1 = max(0, int(y1) - margin)
                x2 = min(w, int(x2) + margin)
                y2 = min(h, int(y2) + margin)

                # 提取区域
                region = image[y1:y2, x1:x2]

                text_regions.append({
                    'region': region,
                    'bbox': (x1, y1, x2, y2),
                    'confidence': confidence,
                    'class_id': class_id
                })

        return text_regions

    def recognize_text_easyocr(self, image_region):
        """
        使用EasyOCR识别文字
        """
        if 'easyocr' not in self.ocr_engines:
            return []

        try:
            results = self.ocr_engines['easyocr'].readtext(image_region)

            text_results = []
            for (bbox, text, confidence) in results:
                if confidence > self.ocr_confidence_threshold:
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': bbox,
                        'engine': 'easyocr'
                    })

            return text_results
        except Exception as e:
            print(f"EasyOCR识别错误: {e}")
            return []

    def recognize_text_cnocr(self, image_region):
        """
        使用CnOCR识别文字
        """
        if 'cnocr' not in self.ocr_engines:
            return []

        try:
            # CnOCR需要PIL图像格式
            from PIL import Image
            if isinstance(image_region, type(None)) or image_region.size == 0:
                return []

            # 转换为PIL图像
            if len(image_region.shape) == 3:
                image_pil = Image.fromarray(cv2.cvtColor(image_region, cv2.COLOR_BGR2RGB))
            else:
                image_pil = Image.fromarray(image_region)

            # 使用CnOCR进行识别
            results = self.ocr_engines['cnocr'].ocr(image_pil)

            text_results = []
            for result in results:
                text = result.get('text', '')
                confidence = result.get('score', 0.0)

                if confidence > self.ocr_confidence_threshold and text.strip():
                    text_results.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': result.get('position', []),
                        'engine': 'cnocr'
                    })

            return text_results
        except Exception as e:
            print(f"CnOCR识别错误: {e}")
            return []

    def ensemble_ocr_results(self, easyocr_results, cnocr_results):
        """
        融合多个OCR引擎的结果以提高精度
        """
        all_results = easyocr_results + cnocr_results

        # 按置信度排序
        all_results.sort(key=lambda x: x['confidence'], reverse=True)

        # 去重和融合
        final_results = []
        for result in all_results:
            # 简单的去重策略：如果文字内容相似度高，选择置信度更高的
            is_duplicate = False
            for existing in final_results:
                if self.text_similarity(result['text'], existing['text']) > 0.8:
                    is_duplicate = True
                    break

            if not is_duplicate:
                final_results.append(result)

        return final_results

    def text_similarity(self, text1, text2):
        """
        计算两个文本的相似度
        """
        if not text1 or not text2:
            return 0.0

        # 简单的字符级相似度计算
        set1 = set(text1.lower())
        set2 = set(text2.lower())

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0

    def predict(self, image_path, save_result=True, output_dir='results'):
        """
        综合预测：目标检测 + OCR文字识别
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        # 目标检测
        detection_results = self.detect_objects(image_path)

        # 提取文字区域
        text_regions = self.extract_text_regions(image, detection_results)

        # OCR文字识别
        all_text_results = []
        for region_info in text_regions:
            region = region_info['region']

            # 使用多个OCR引擎
            easyocr_results = self.recognize_text_easyocr(region)
            cnocr_results = self.recognize_text_cnocr(region)

            # 融合结果
            ensemble_results = self.ensemble_ocr_results(easyocr_results, cnocr_results)

            # 添加区域信息
            for text_result in ensemble_results:
                text_result.update({
                    'detection_bbox': region_info['bbox'],
                    'detection_confidence': region_info['confidence'],
                    'detection_class_id': region_info['class_id']
                })

            all_text_results.extend(ensemble_results)

        # 整合结果
        final_result = {
            'image_path': image_path,
            'detections': detection_results,
            'text_recognition': all_text_results,
            'timestamp': time.time()
        }

        # 保存结果
        if save_result:
            self.save_prediction_result(final_result, image, output_dir)

        return final_result

    def save_prediction_result(self, result, image, output_dir):
        """
        保存预测结果（可视化图像和JSON数据）
        """
        os.makedirs(output_dir, exist_ok=True)

        # 获取文件名
        image_name = Path(result['image_path']).stem

        # 在图像上绘制结果
        result_image = image.copy()

        # 绘制目标检测框
        if len(result['detections']) > 0 and len(result['detections'][0].boxes) > 0:
            boxes = result['detections'][0].boxes
            for box in boxes:
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                confidence = float(box.conf[0])
                class_id = int(box.cls[0])

                # 绘制检测框
                cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(result_image, f'Class:{class_id} {confidence:.2f}',
                           (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 绘制OCR结果
        for text_result in result['text_recognition']:
            bbox = text_result['detection_bbox']
            text = text_result['text']
            confidence = text_result['confidence']

            # 绘制文字区域
            cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), (255, 0, 0), 2)

            # 添加识别的文字
            cv2.putText(result_image, f'{text} ({confidence:.2f})',
                       (bbox[0], bbox[3]+20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

        # 保存可视化结果
        cv2.imwrite(os.path.join(output_dir, f'{image_name}_result.jpg'), result_image)

        # 保存JSON结果
        json_result = {
            'image_path': result['image_path'],
            'detections': [
                {
                    'bbox': box.xyxy[0].cpu().numpy().tolist(),
                    'confidence': float(box.conf[0]),
                    'class_id': int(box.cls[0])
                } for box in result['detections'][0].boxes
            ] if len(result['detections']) > 0 and len(result['detections'][0].boxes) > 0 else [],
            'text_recognition': result['text_recognition'],
            'timestamp': result['timestamp']
        }

        with open(os.path.join(output_dir, f'{image_name}_result.json'), 'w', encoding='utf-8') as f:
            json.dump(json_result, f, ensure_ascii=False, indent=2)


def train_yolo_model():
    """
    训练YOLO目标检测模型
    """
    print("🚀 开始训练YOLO目标检测模型...")

    # 检查GPU可用性
    device = '0' if torch.cuda.is_available() else 'cpu'
    print(f"📱 使用设备: {'GPU' if device == '0' else 'CPU'}")

    # 加载预训练模型
    model = YOLO(r'yolo11s.pt')

    # 高精度训练参数配置
    training_args = {
        'data': r'yqjdataset\data.yaml',
        'epochs': 150,  # 增加训练轮数以提高精度
        'imgsz': 640,
        'batch': 16,
        'device': device,
        'optimizer': 'AdamW',
        'lr0': 0.001,
        'lrf': 0.01,  # 最终学习率
        'momentum': 0.937,
        'weight_decay': 0.0005,
        'warmup_epochs': 3.0,
        'warmup_momentum': 0.8,
        'warmup_bias_lr': 0.1,
        'close_mosaic': 15,
        'workers': 4,
        'amp': True,  # 自动混合精度
        'single_cls': False,
        'project': 'high_precision_detection',
        'name': 'yolo11s_ocr_integrated',
        'save': True,
        'save_period': 10,  # 每10个epoch保存一次
        'val': True,
        'plots': True,
        'verbose': True,
        # 数据增强参数 - 提高模型泛化能力
        'hsv_h': 0.015,
        'hsv_s': 0.7,
        'hsv_v': 0.4,
        'degrees': 0.0,
        'translate': 0.1,
        'scale': 0.5,
        'shear': 0.0,
        'perspective': 0.0,
        'flipud': 0.0,
        'fliplr': 0.5,
        'mosaic': 1.0,
        'mixup': 0.0,
        'copy_paste': 0.0,
        # 损失函数权重
        'box': 7.5,
        'cls': 0.5,
        'dfl': 1.5,
    }

    # 开始训练
    results = model.train(**training_args)

    print("✅ YOLO模型训练完成!")
    return model, results


def create_integrated_model(yolo_model_path=None):
    """
    创建整合的多任务模型
    """
    if yolo_model_path is None:
        # 使用训练好的最佳模型
        yolo_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'

        # 如果不存在，使用预训练模型
        if not os.path.exists(yolo_model_path):
            yolo_model_path = 'yolo11s.pt'

    print(f"🔧 创建整合模型，使用YOLO权重: {yolo_model_path}")

    # 创建多任务模型
    integrated_model = MultiTaskModel(yolo_model_path)

    print("✅ 整合模型创建完成!")
    return integrated_model


def test_integrated_model(model, test_images_dir='yqjdataset/test/images', output_dir='integrated_results'):
    """
    测试整合模型的性能
    """
    print(f"🧪 开始测试整合模型...")

    # 获取测试图像
    test_images = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        test_images.extend(Path(test_images_dir).glob(ext))

    if not test_images:
        print(f"❌ 在 {test_images_dir} 中未找到测试图像")
        return

    print(f"📊 找到 {len(test_images)} 张测试图像")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 测试模型
    results = []
    for i, image_path in enumerate(tqdm(test_images[:10], desc="测试进度")):  # 测试前10张图像
        try:
            result = model.predict(str(image_path), save_result=True, output_dir=output_dir)
            results.append(result)

            # 打印结果摘要
            detection_count = len(result['detections'][0].boxes) if len(result['detections']) > 0 else 0
            text_count = len(result['text_recognition'])

            print(f"图像 {i+1}: 检测到 {detection_count} 个目标, 识别到 {text_count} 段文字")

        except Exception as e:
            print(f"❌ 处理图像 {image_path} 时出错: {e}")

    print(f"✅ 测试完成! 结果保存在 {output_dir}")
    return results


def main():
    """
    主函数：训练和测试整合模型
    """
    print("🎯 高精度目标检测+OCR文字识别整合训练系统")
    print("=" * 60)

    # 步骤1: 训练YOLO模型
    print("\n📍 步骤1: 训练高精度YOLO目标检测模型")
    yolo_model, training_results = train_yolo_model()

    # 步骤2: 创建整合模型
    print("\n📍 步骤2: 创建目标检测+OCR整合模型")
    integrated_model = create_integrated_model()

    # 步骤3: 测试整合模型
    print("\n📍 步骤3: 测试整合模型性能")
    test_results = test_integrated_model(integrated_model)

    print("\n🎉 训练和测试完成!")
    print("📁 检查以下目录获取结果:")
    print("   - high_precision_detection/yolo11s_ocr_integrated/ (YOLO训练结果)")
    print("   - integrated_results/ (整合模型测试结果)")

    return integrated_model


if __name__ == '__main__':
    # 运行主程序
    model = main()