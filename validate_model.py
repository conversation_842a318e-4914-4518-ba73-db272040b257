#!/usr/bin/env python3
"""
模型验证脚本
验证整合的目标检测+OCR模型在指定图像上的性能
测试图像: DaYuanTuZ_0.png
"""

import warnings
import os
import cv2
import torch
import json
import time
from pathlib import Path

warnings.filterwarnings('ignore')

# 导入训练脚本中的模型类
try:
    from train import MultiTaskModel, create_integrated_model
    TRAIN_MODULE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  无法导入训练模块: {e}")
    print("请确保train.py文件在同一目录下")
    TRAIN_MODULE_AVAILABLE = False

def validate_single_image(image_path="DaYuanTuZ_0.png", model_path=None):
    """
    验证单张图像的检测和OCR效果
    
    Args:
        image_path: 要验证的图像路径
        model_path: 训练好的模型路径，如果为None则使用默认路径
    """
    print("🔍 开始验证模型性能...")
    print(f"📷 验证图像: {image_path}")
    print("=" * 60)
    
    # 检查图像是否存在
    if not os.path.exists(image_path):
        print(f"❌ 错误: 找不到图像文件 {image_path}")
        print("💡 请确保图像文件在当前目录下")
        return None
    
    # 创建整合模型
    try:
        print("🔧 正在加载整合模型...")
        
        # 如果没有指定模型路径，尝试使用训练好的模型
        if model_path is None:
            # 优先使用训练好的最佳模型
            trained_model_path = 'high_precision_detection/yolo11s_ocr_integrated/weights/best.pt'
            if os.path.exists(trained_model_path):
                model_path = trained_model_path
                print(f"✅ 找到训练好的模型: {model_path}")
            else:
                # 使用预训练模型
                model_path = 'yolo11s.pt'
                print(f"⚠️  使用预训练模型: {model_path}")
        
        # 创建模型
        model = create_integrated_model(model_path)
        print("✅ 模型加载成功!")
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None
    
    # 开始验证
    try:
        print(f"\n🚀 开始处理图像: {image_path}")
        start_time = time.time()
        
        # 执行预测
        result = model.predict(
            image_path, 
            save_result=True, 
            output_dir='validation_results'
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱️  处理时间: {processing_time:.2f} 秒")
        
        # 分析结果
        analyze_results(result, processing_time)
        
        return result
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return None

def analyze_results(result, processing_time):
    """
    分析和展示验证结果
    
    Args:
        result: 模型预测结果
        processing_time: 处理时间
    """
    print("\n📊 验证结果分析:")
    print("-" * 40)
    
    # 目标检测结果分析
    detection_count = 0
    if len(result['detections']) > 0 and len(result['detections'][0].boxes) > 0:
        detection_count = len(result['detections'][0].boxes)
        
        print(f"🎯 目标检测结果:")
        print(f"   检测到目标数量: {detection_count}")
        
        # 显示每个检测目标的详细信息
        for i, box in enumerate(result['detections'][0].boxes):
            confidence = float(box.conf[0])
            class_id = int(box.cls[0])
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
            
            print(f"   目标 {i+1}: 类别ID={class_id}, 置信度={confidence:.3f}, "
                  f"位置=({x1:.0f},{y1:.0f},{x2:.0f},{y2:.0f})")
    else:
        print("🎯 目标检测结果: 未检测到任何目标")
    
    # OCR文字识别结果分析
    text_count = len(result['text_recognition'])
    print(f"\n📝 OCR文字识别结果:")
    print(f"   识别到文字段数: {text_count}")
    
    if text_count > 0:
        print("   识别内容详情:")
        for i, text_result in enumerate(result['text_recognition']):
            text = text_result['text']
            confidence = text_result['confidence']
            engine = text_result['engine']
            
            print(f"   文字 {i+1}: \"{text}\" (置信度={confidence:.3f}, 引擎={engine})")
    else:
        print("   未识别到任何文字")
    
    # 性能统计
    print(f"\n⚡ 性能统计:")
    print(f"   总处理时间: {processing_time:.2f} 秒")
    print(f"   平均每个目标: {processing_time/max(detection_count, 1):.3f} 秒")
    print(f"   平均每段文字: {processing_time/max(text_count, 1):.3f} 秒")
    
    # 结果文件保存信息
    print(f"\n💾 结果保存:")
    image_name = Path(result['image_path']).stem
    print(f"   可视化结果: validation_results/{image_name}_result.jpg")
    print(f"   JSON数据: validation_results/{image_name}_result.json")

def validate_with_different_thresholds(image_path="DaYuanTuZ_0.png"):
    """
    使用不同置信度阈值验证模型性能
    
    Args:
        image_path: 要验证的图像路径
    """
    print("\n🔬 多阈值验证测试...")
    print("=" * 60)
    
    # 不同的置信度阈值
    thresholds = [0.1, 0.25, 0.5, 0.7, 0.9]
    
    try:
        # 创建模型
        model = create_integrated_model()
        
        for threshold in thresholds:
            print(f"\n📊 测试置信度阈值: {threshold}")
            
            # 临时修改模型的置信度阈值
            original_threshold = model.ocr_confidence_threshold
            model.ocr_confidence_threshold = threshold
            
            # 执行检测（不保存结果）
            detection_results = model.detect_objects(image_path, conf_threshold=threshold)
            
            # 统计结果
            detection_count = 0
            if len(detection_results) > 0 and len(detection_results[0].boxes) > 0:
                detection_count = len(detection_results[0].boxes)
            
            print(f"   检测到目标数量: {detection_count}")
            
            # 恢复原始阈值
            model.ocr_confidence_threshold = original_threshold
            
    except Exception as e:
        print(f"❌ 多阈值验证失败: {e}")

def main():
    """
    主验证函数
    """
    print("🎯 高精度目标检测+OCR模型验证系统")
    print("=" * 60)
    
    # 验证指定图像
    image_path = "DaYuanTuZ_0.png"
    
    print(f"📋 验证配置:")
    print(f"   目标图像: {image_path}")
    print(f"   输出目录: validation_results/")
    print(f"   GPU可用: {'是' if torch.cuda.is_available() else '否'}")
    
    # 执行单图像验证
    result = validate_single_image(image_path)
    
    if result is not None:
        print("\n✅ 单图像验证完成!")
        
        # 执行多阈值验证
        validate_with_different_thresholds(image_path)
        
        print("\n🎉 所有验证测试完成!")
        print("\n📁 查看结果:")
        print("   - validation_results/ 目录包含可视化结果")
        print("   - 检查控制台输出了解详细性能数据")
        
    else:
        print("\n❌ 验证失败，请检查:")
        print("   1. 图像文件是否存在")
        print("   2. 模型文件是否正确")
        print("   3. 依赖包是否完整安装")

if __name__ == "__main__":
    main()
